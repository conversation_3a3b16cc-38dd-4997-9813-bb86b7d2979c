EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT="587"
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# Neon Database
DATABASE_URL="****************************************************"

# NextAuth.js Credentials
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"
# Generate a strong secret using: openssl rand -base64 32
NEXTAUTH_SECRET="YOUR_STRONG_RANDOM_SECRET"

# Stripe Payment Integration
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key"
STRIPE_WEBHOOK_SECRET="whsec_your_stripe_webhook_secret"
NEXT_PUBLIC_STRIPE_PRICE_ID="price_your_stripe_price_id"

# AI STUFF - Groq
GROQ_API_KEY="your_groq_api_key"

NEXT_PUBLIC_APP_URL="http://localhost:3000"
