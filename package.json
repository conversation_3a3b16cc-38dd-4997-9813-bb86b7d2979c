{"name": "b360", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio --port 3001", "db:migrate": "ts-node src/scripts/run-migrations.ts"}, "dependencies": {"@auth/drizzle-adapter": "^1.8.0", "@googlemaps/js-api-loader": "^1.16.10", "@hookform/resolvers": "^5.1.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/stripe-js": "^7.8.0", "@tabler/icons-react": "^3.31.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotted-map": "^2.2.3", "drizzle-orm": "^0.41.0", "framer-motion": "^12.23.0", "groq-sdk": "^0.3.3", "lenis": "^1.2.3", "lucide-react": "^0.525.0", "motion": "^12.23.6", "next": "^15.3.5", "next-auth": "^4.24.5", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "pg": "^8.11.3", "postgres": "^3.4.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "recharts": "^2.15.3", "sonner": "^2.0.5", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "uuid": "^9.0.1", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3.0.0", "@tailwindcss/postcss": "^4", "@types/node": "^20.10.6", "@types/nodemailer": "^6.4.17", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "dotenv": "^16.3.1", "drizzle-kit": "^0.31.1", "eslint": "^9.0.0", "eslint-config-next": "15.3.5", "postcss": "^8.4.31", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.5", "typescript": "^5.3.3"}}